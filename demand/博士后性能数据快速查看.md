# 博士后推荐算法性能数据快速查看

## 🚀 快速命令

### 查看实时性能数据
```bash
cd /Users/<USER>/project/gc/new_gaoxiao_yii

# 查看最新性能日志
tail -20 log/debug/postdocAnnouncementRecommend.log

# 运行基础压测
php console/scripts/postdoc_enhanced_test.php announcement 2 3

# 运行综合压测（推荐）
php console/scripts/postdoc_comprehensive_test.php announcement 2 8

# 查看日志状态
php yii postdoc-performance-test/log-status
```

## ⚠️ **重要发现：严重性能问题**

### 复杂查询性能问题
```
🔴 关键词搜索: 21587ms (21秒) ← 严重超时
🔴 地区筛选: 2578-3494ms (2-3秒) ← 超时
🟢 基础查询: 1-11ms ← 正常
```

**影响**：用户使用关键词搜索和地区筛选时体验极差

## 📊 关键性能指标

### 博士后公告推荐
- **平均处理时间**：1054.61ms（20条数据）
- **单条处理时间**：52.73ms/条
- **首次加载**：8991ms（含初始化）
- **缓存后**：15-94ms

### 性能瓶颈排序
1. **专业信息查询**：39.3%（417ms）← 最大瓶颈
2. **地区信息查询**：31.5%（334ms）
3. **招聘人数查询**：29.1%（303ms）

### 场景性能对比
- **第1页**：47.84ms ✅ 优秀
- **第2页**：17.00ms ✅ 优秀  
- **第3页**：1.59ms ✅ 极佳
- **第5页**：3459.08ms ❌ 需优化

## 💡 优化建议（按优先级）

### P0：专业信息查询优化
```php
// 当前：循环单条查询
foreach ($announcementIds as $id) {
    $majorMap[$id] = BaseJobMajorRelation::getAnnouncementMajorText($id);
}

// 建议：批量查询
$majorRelations = BaseJobMajorRelation::find()
    ->where(['announcement_id' => $announcementIds])
    ->with('major')
    ->all();
```
**预期提升**：60-80%

### P1：地区信息查询优化
```php
// 建议：批量查询 + 缓存
$areaMap = Cache::getOrSet('area_' . md5(implode(',', $ids)), function() {
    return $this->batchGetAreaInfo($announcementIds);
}, 1800);
```
**预期提升**：50-70%

### P1：招聘人数查询优化
```php
// 建议：聚合查询
$counts = BaseJob::find()
    ->select(['announcement_id', 'SUM(recruit_amount) as total'])
    ->where(['announcement_id' => $announcementIds])
    ->groupBy('announcement_id')
    ->asArray()
    ->all();
```
**预期提升**：40-60%

## 🎯 优化效果预期

| 项目 | 当前 | 优化后 | 提升 |
|------|------|--------|------|
| 专业查询 | 417ms | 83ms | 80% |
| 地区查询 | 334ms | 100ms | 70% |
| 人数查询 | 303ms | 121ms | 60% |
| **总体** | **1055ms** | **305ms** | **71%** |

## 🛠️ 常用压测命令

```bash
# 基础压测
php yii postdoc-performance-test/announcement-recommend 3 3

# 增强版压测（推荐）
php console/scripts/postdoc_enhanced_test.php announcement 3 4

# 全面压测
php console/scripts/postdoc_enhanced_test.php all 3 4

# 清理日志
php yii postdoc-performance-test/clear-logs
```

## 📈 性能监控

### 实时监控
```bash
# 监控性能日志
tail -f log/debug/postdocAnnouncementRecommend.log

# 查看性能分解
grep "详细耗时分解" log/debug/postdocAnnouncementRecommend.log -A 10
```

### 性能警告阈值
- **正常**：< 500ms
- **需关注**：500ms - 1000ms  
- **需优化**：> 1000ms
- **严重**：> 3000ms

## 🔍 问题诊断

### 如果性能突然变差
1. 检查数据量是否增加
2. 检查数据库连接是否正常
3. 检查缓存是否失效
4. 运行压测对比历史数据

### 如果某个环节特别慢
1. 查看详细性能分解日志
2. 识别占比最高的操作
3. 检查相关数据库表的索引
4. 考虑添加缓存策略

## 📋 相关文件

- **服务文件**：`common/service/boShiHouColumn/AnnouncementService.php`
- **压测控制器**：`console/controllers/PostdocPerformanceTestController.php`
- **压测脚本**：`console/scripts/postdoc_enhanced_test.php`
- **性能日志**：`log/debug/postdocAnnouncementRecommend.log`
- **详细报告**：`demand/博士后推荐算法性能分析报告.md`

## 🎉 快速总结

- **主要瓶颈**：专业信息查询（39.3%）
- **优化潜力**：71%性能提升空间
- **工具完备**：独立压测和监控体系
- **数据充分**：详细性能分解数据

**下一步**：按优先级实施优化，使用压测工具验证效果

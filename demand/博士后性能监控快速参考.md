# 博士后性能监控快速参考卡片

## 🚀 常用命令

```bash
cd /Users/<USER>/project/gc/new_gaoxiao_yii

# === 快速测试 ===
# 公告推荐基础测试
php yii postdoc-performance-test/announcement-recommend 2 3

# 公告推荐综合测试（推荐）
php console/scripts/postdoc_comprehensive_test.php announcement 2 10

# 职位推荐测试
php yii postdoc-performance-test/job-recommend 2 3

# === 日志查看 ===
# 🎯 重点关注：博士后慢查询日志（推荐）
tail -f log/debug/postdocSlowQuery.log

# 实时监控完整日志
tail -f log/debug/postdocAnnouncementRecommend.log

# 查看最近性能
tail -20 log/debug/postdocAnnouncementRecommend.log

# 统计博士后慢查询数量
grep "🐌" log/debug/postdocSlowQuery.log | wc -l
```

## 📊 性能等级

| 时间范围 | 等级 | 状态 | 说明 |
|---------|------|------|------|
| < 100ms | 🟢 | 优秀 | 用户体验极佳 |
| 100-500ms | 🟡 | 良好 | 用户体验良好 |
| 500-2000ms | 🟠 | 一般 | 需要关注 |
| > 2000ms | 🔴 | 较差 | 急需优化 |

## 🎯 关键指标

### 当前性能表现
- **缓存命中**: 21ms (🟢 优秀)
- **基础查询**: 100-500ms (🟡 良好)  
- **复杂查询**: 2000-10000ms (🔴 需优化)
- **关键词搜索**: 20000ms+ (🔴 严重超时)

### 优化成果
- **数据处理**: 1491ms → 110ms (92.6%提升)
- **缓存效果**: 230倍性能提升
- **批量查询**: 解决N+1查询问题

## 🔍 日志解读

### 正常日志示例
```
=== 博士后公告推荐算法完整性能分析 ===
查询条件:
  - 关键词搜索: 博士后
返回数据量: 20条
总执行时间: 1500ms ← 关键指标
数据来源: 🚀 缓存命中 ← 缓存状态
详细耗时分解:
  - 主要公告数据查询: 1200ms (80%) ← 主要瓶颈
  - 数据处理和格式化: 100ms (6.7%) ← 已优化
```

### 异常日志示例
```
=== 博士后公告推荐算法执行异常 ===
异常信息: SQLSTATE[42000]: Syntax error ← 错误信息
执行时间: 1500ms ← 异常前执行时间
查询条件: [具体参数] ← 出错时的参数
```

## ⚠️ 告警阈值

### 性能告警
- 总执行时间 > 5秒：🟠 需要关注
- 总执行时间 > 10秒：🔴 紧急优化
- 连续3次超时：🚨 系统异常

### 监控脚本
```bash
# 简单的性能检查
LATEST_TIME=$(grep "总执行时间" log/debug/postdocAnnouncementRecommend.log | tail -1 | awk -F': ' '{print $2}' | awk -F'ms' '{print $1}')
if [ $(echo "$LATEST_TIME > 5000" | bc) -eq 1 ]; then
    echo "⚠️ 性能告警：${LATEST_TIME}ms"
fi
```

## 🛠️ 故障排查

### 1. "有头无尾"日志
**现象**: 只有开始记录，没有完整分析
**原因**: 程序异常中断
**解决**: 检查错误日志，查看异常信息

### 2. 查询超时
**现象**: 执行时间 > 10秒
**原因**: SQL查询效率低，缺少索引
**解决**: 优化查询，添加索引

### 3. 缓存失效
**现象**: 基础查询时间突然变长
**原因**: 缓存服务异常
**解决**: 重启缓存服务，检查缓存配置

## 📈 性能优化清单

### ✅ 已完成
- [x] 数据处理批量查询优化
- [x] 专业信息查询优化 (92.1%提升)
- [x] 地区信息查询优化 (86.2%提升)
- [x] 招聘人数查询优化 (95.4%提升)
- [x] 缓存策略优化 (230倍提升)

### 🔄 进行中
- [ ] 主要数据查询SQL优化
- [ ] 关键词搜索索引优化
- [ ] 数据组装算法优化

### 📋 待优化
- [ ] 添加Elasticsearch搜索引擎
- [ ] 实现查询结果缓存
- [ ] 数据库读写分离
- [ ] 异步查询处理

## 🎯 日常维护

### 每日检查
```bash
# 1. 检查最近性能
tail -10 log/debug/postdocAnnouncementRecommend.log

# 2. 统计平均响应时间
grep "总执行时间" log/debug/postdocAnnouncementRecommend.log | tail -100 | \
awk -F'总执行时间: ' '{sum+=$2} END {print "平均:", sum/NR "ms"}'

# 3. 查找异常
grep "异常" log/debug/postdocAnnouncementRecommend.log | tail -5
```

### 每周检查
```bash
# 1. 性能趋势分析
php console/scripts/postdoc_comprehensive_test.php announcement 3 15

# 2. 缓存命中率统计
grep "缓存命中" log/debug/postdocAnnouncementRecommend.log | wc -l

# 3. 慢查询统计
grep "总执行时间" log/debug/postdocAnnouncementRecommend.log | \
awk -F'总执行时间: ' '$2 > 2000 {count++} END {print "慢查询数量:", count}'
```

## 📞 快速联系

### 文件位置
- 📁 监控文档: `demand/博士后推荐算法性能监控使用文档.md`
- 📁 慢查询文档: `demand/博士后慢查询监控使用说明.md`
- 📁 日志目录: `log/debug/`
- 📁 慢查询日志: `log/debug/postdocSlowQuery.log`
- 📁 测试脚本: `console/scripts/`

### 关键类文件
- 🔧 公告服务: `common/service/boShiHouColumn/AnnouncementService.php`
- 🔧 职位服务: `common/service/boShiHouColumn/JobService.php`
- 🔧 调试助手: `common/helpers/DebugHelper.php`

---
**快速参考版本**: v1.0 | **更新**: 2025-08-04

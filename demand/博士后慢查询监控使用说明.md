# 博士后推荐算法慢查询监控使用说明

## 📋 概述

慢查询监控专门记录执行时间超过2000ms的查询，帮助你快速定位性能问题。

## 🎯 慢查询日志位置

```bash
# 慢查询专用日志文件
log/debug/postdocSlowQuery.log
```

## 📊 慢查询日志格式

### 日志示例
```
[2025-08-04 13:58:52] 🐌 博士后公告推荐慢查询
执行时间: 10955.94ms
返回数据量: 20条
主要瓶颈: 主要公告数据查询 (8865.58ms)
性能分解:
  - 参数处理和缓存检查: 0.01ms (0%)
  - 查询条件构建: 126.32ms (1.2%)
  - 关键词子查询执行: 109.39ms (1%)
  - 置顶公告查询: 70.25ms (0.6%)
  - 主要公告数据查询: 8865.58ms (80.9%) ← 主要瓶颈
  - 数据组装和分页: 1784.48ms (16.3%)
  - 数据处理和格式化: 106.77ms (1%)
记录时间: 2025-08-04 13:58:52
------------------------------------------------------------
```

### 字段说明
- **🐌 类型标识**: 博士后公告推荐 / 博士后职位推荐
- **执行时间**: 总执行时间（超过2000ms才记录）
- **返回数据量**: 查询返回的数据条数
- **主要瓶颈**: 耗时最长的执行阶段
- **性能分解**: 各阶段详细耗时和占比

## 🚀 常用监控命令

### 实时监控慢查询
```bash
cd /Users/<USER>/project/gc/new_gaoxiao_yii

# 实时查看慢查询日志
tail -f log/debug/postdocSlowQuery.log

# 查看最近的慢查询
tail -20 log/debug/postdocSlowQuery.log

# 查看今天的慢查询
grep "$(date +%Y-%m-%d)" log/debug/postdocSlowQuery.log
```

### 慢查询统计分析
```bash
# 统计慢查询总数
grep "🐌" log/debug/postdocSlowQuery.log | wc -l

# 统计今天的慢查询数量
grep "$(date +%Y-%m-%d)" log/debug/postdocSlowQuery.log | grep "🐌" | wc -l

# 查找最慢的查询
grep "执行时间:" log/debug/postdocSlowQuery.log | sort -k2 -nr | head -5

# 统计慢查询的主要瓶颈
grep "主要瓶颈:" log/debug/postdocSlowQuery.log | sort | uniq -c | sort -nr
```

### 慢查询趋势分析
```bash
# 按小时统计慢查询数量
grep "🐌" log/debug/postdocSlowQuery.log | awk -F'[' '{print $2}' | awk -F']' '{print $1}' | awk -F' ' '{print $2}' | awk -F':' '{print $1}' | sort | uniq -c

# 查看慢查询的平均执行时间
grep "执行时间:" log/debug/postdocSlowQuery.log | awk -F'执行时间: ' '{print $2}' | awk -F'ms' '{sum+=$1; count++} END {print "平均慢查询时间:", sum/count "ms"}'
```

## 🎯 性能问题定位

### 根据主要瓶颈分类

#### 1. 主要公告数据查询瓶颈 (80%+)
```
主要瓶颈: 主要公告数据查询 (8865.58ms)
```
**问题**: SQL查询效率低
**解决方案**:
- 检查数据库索引
- 优化JOIN查询
- 考虑查询分解
- 添加查询缓存

#### 2. 数据组装和分页瓶颈 (15%+)
```
主要瓶颈: 数据组装和分页 (1784.48ms)
```
**问题**: 数据处理算法效率低
**解决方案**:
- 优化排序算法
- 减少数组操作
- 使用更高效的数据结构

#### 3. 关键词子查询瓶颈 (10%+)
```
主要瓶颈: 关键词子查询执行 (2100ms)
```
**问题**: 关键词搜索效率低
**解决方案**:
- 添加全文索引
- 使用搜索引擎
- 优化LIKE查询

## 📈 慢查询告警设置

### 简单告警脚本
```bash
#!/bin/bash
# 慢查询告警脚本

SLOW_LOG="log/debug/postdocSlowQuery.log"
ALERT_THRESHOLD=5  # 5分钟内超过5次慢查询就告警

# 检查最近5分钟的慢查询数量
RECENT_SLOW_COUNT=$(grep "$(date -d '5 minutes ago' '+%Y-%m-%d %H:%M')" $SLOW_LOG | grep "🐌" | wc -l)

if [ $RECENT_SLOW_COUNT -gt $ALERT_THRESHOLD ]; then
    echo "🚨 慢查询告警：最近5分钟内有 $RECENT_SLOW_COUNT 次慢查询"
    # 这里可以添加邮件或短信通知
fi
```

### 高级监控脚本
```bash
#!/bin/bash
# 慢查询监控面板

echo "📊 慢查询监控面板 - $(date)"
echo "=================================="

# 今天的慢查询统计
TODAY_SLOW=$(grep "$(date +%Y-%m-%d)" log/debug/postdocSlowQuery.log | grep "🐌" | wc -l)
echo "今日慢查询总数: $TODAY_SLOW"

# 最近1小时的慢查询
HOUR_AGO=$(date -d '1 hour ago' '+%Y-%m-%d %H')
RECENT_SLOW=$(grep "$HOUR_AGO" log/debug/postdocSlowQuery.log | grep "🐌" | wc -l)
echo "最近1小时慢查询: $RECENT_SLOW"

# 最慢的查询
echo "最慢的查询:"
grep "执行时间:" log/debug/postdocSlowQuery.log | sort -k2 -nr | head -3

# 主要瓶颈统计
echo "主要瓶颈分布:"
grep "主要瓶颈:" log/debug/postdocSlowQuery.log | awk -F': ' '{print $2}' | sort | uniq -c | sort -nr | head -5
```

## 🛠️ 日常维护

### 每日检查清单
```bash
# 1. 查看昨天的慢查询数量
grep "$(date -d yesterday +%Y-%m-%d)" log/debug/postdocSlowQuery.log | grep "🐌" | wc -l

# 2. 查看最新的慢查询
tail -5 log/debug/postdocSlowQuery.log

# 3. 检查是否有新的瓶颈类型
grep "主要瓶颈:" log/debug/postdocSlowQuery.log | tail -10 | awk -F': ' '{print $2}' | sort | uniq
```

### 每周分析报告
```bash
#!/bin/bash
# 生成慢查询周报

echo "📊 慢查询周报 ($(date -d '7 days ago' +%Y-%m-%d) - $(date +%Y-%m-%d))"
echo "=================================================="

# 本周慢查询总数
WEEK_SLOW=$(grep -E "$(date -d '7 days ago' +%Y-%m-%d)|$(date -d '6 days ago' +%Y-%m-%d)|$(date -d '5 days ago' +%Y-%m-%d)|$(date -d '4 days ago' +%Y-%m-%d)|$(date -d '3 days ago' +%Y-%m-%d)|$(date -d '2 days ago' +%Y-%m-%d)|$(date -d '1 day ago' +%Y-%m-%d)|$(date +%Y-%m-%d)" log/debug/postdocSlowQuery.log | grep "🐌" | wc -l)
echo "本周慢查询总数: $WEEK_SLOW"

# 平均每天慢查询数量
echo "平均每天慢查询: $((WEEK_SLOW / 7))"

# 最慢的查询TOP5
echo "本周最慢查询TOP5:"
grep -E "$(date -d '7 days ago' +%Y-%m-%d)|$(date -d '6 days ago' +%Y-%m-%d)|$(date -d '5 days ago' +%Y-%m-%d)|$(date -d '4 days ago' +%Y-%m-%d)|$(date -d '3 days ago' +%Y-%m-%d)|$(date -d '2 days ago' +%Y-%m-%d)|$(date -d '1 day ago' +%Y-%m-%d)|$(date +%Y-%m-%d)" log/debug/postdocSlowQuery.log | grep "执行时间:" | sort -k2 -nr | head -5

# 瓶颈分析
echo "主要瓶颈分析:"
grep -E "$(date -d '7 days ago' +%Y-%m-%d)|$(date -d '6 days ago' +%Y-%m-%d)|$(date -d '5 days ago' +%Y-%m-%d)|$(date -d '4 days ago' +%Y-%m-%d)|$(date -d '3 days ago' +%Y-%m-%d)|$(date -d '2 days ago' +%Y-%m-%d)|$(date -d '1 day ago' +%Y-%m-%d)|$(date +%Y-%m-%d)" log/debug/postdocSlowQuery.log | grep "主要瓶颈:" | awk -F': ' '{print $2}' | sort | uniq -c | sort -nr
```

## 🎯 优化建议

### 根据慢查询日志优化

1. **如果主要瓶颈是"主要公告数据查询"**:
   ```sql
   -- 检查并添加索引
   SHOW INDEX FROM announcement;
   SHOW INDEX FROM job;
   SHOW INDEX FROM company;
   
   -- 建议的索引
   ALTER TABLE announcement ADD INDEX idx_status_refresh (status, refresh_time);
   ALTER TABLE job ADD INDEX idx_announcement_status (announcement_id, status);
   ```

2. **如果主要瓶颈是"关键词子查询执行"**:
   ```sql
   -- 添加全文索引
   ALTER TABLE job ADD FULLTEXT(name);
   ALTER TABLE announcement ADD FULLTEXT(title);
   ```

3. **如果主要瓶颈是"数据组装和分页"**:
   - 优化PHP代码中的数组操作
   - 减少不必要的数据处理
   - 考虑使用更高效的排序算法

## 📞 快速参考

### 关键命令
```bash
# 查看慢查询日志
tail -f log/debug/postdocSlowQuery.log

# 统计慢查询数量
grep "🐌" log/debug/postdocSlowQuery.log | wc -l

# 查找最慢的查询
grep "执行时间:" log/debug/postdocSlowQuery.log | sort -k2 -nr | head -5

# 分析主要瓶颈
grep "主要瓶颈:" log/debug/postdocSlowQuery.log | sort | uniq -c | sort -nr
```

### 告警阈值建议
- **每小时慢查询 > 10次**: 🟡 需要关注
- **每小时慢查询 > 20次**: 🟠 需要优化  
- **每小时慢查询 > 50次**: 🔴 紧急处理

---
**版本**: v1.0 | **更新**: 2025-08-04

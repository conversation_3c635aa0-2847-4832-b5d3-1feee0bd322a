# 博士后慢查询监控快速指南

## 🎯 核心功能

专门监控博士后推荐算法中执行时间超过2000ms的慢查询，帮助快速定位性能瓶颈。

## 📁 关键文件

```bash
# 🎯 重点关注：博士后慢查询日志
log/debug/postdocSlowQuery.log

# 📚 详细文档
demand/博士后慢查询监控使用说明.md
```

## 🚀 快速使用

### 实时监控（推荐）
```bash
cd /Users/<USER>/project/gc/new_gaoxiao_yii

# 🎯 实时查看博士后慢查询
tail -f log/debug/postdocSlowQuery.log
```

### 快速分析
```bash
# 统计慢查询总数
grep "🐌" log/debug/postdocSlowQuery.log | wc -l

# 查看最慢的5个查询
grep "执行时间:" log/debug/postdocSlowQuery.log | sort -k2 -nr | head -5

# 分析主要瓶颈
grep "主要瓶颈:" log/debug/postdocSlowQuery.log | sort | uniq -c | sort -nr
```

## 📊 日志格式示例

```
[2025-08-04 14:06:27] 🐌 博士后公告推荐慢查询
执行时间: 10811.93ms ← 超时时间
返回数据量: 20条
主要瓶颈: 主要公告数据查询 (8704.24ms) ← 重点关注
性能分解:
  - 主要公告数据查询: 8704.24ms (80.5%) ← 主要问题
  - 数据组装和分页: 1818.08ms (16.8%)
  - 查询条件构建: 117.43ms (1.1%)
  - 关键词子查询执行: 99.79ms (0.9%)
  - 数据处理和格式化: 101.29ms (0.9%)
  - 置顶公告查询: 67.94ms (0.6%)
  - 参数处理和缓存检查: 0.02ms (0%)
记录时间: 2025-08-04 14:06:27
------------------------------------------------------------
```

## 🎯 瓶颈类型与解决方案

### 1. 主要公告数据查询瓶颈 (80%+)
**表现**: `主要瓶颈: 主要公告数据查询 (8704ms)`
**解决方案**:
- 检查数据库索引
- 优化JOIN查询
- 考虑查询分解

### 2. 数据组装和分页瓶颈 (15%+)
**表现**: `主要瓶颈: 数据组装和分页 (1818ms)`
**解决方案**:
- 优化排序算法
- 减少数组操作
- 使用更高效的数据结构

### 3. 关键词子查询瓶颈 (10%+)
**表现**: `主要瓶颈: 关键词子查询执行 (2100ms)`
**解决方案**:
- 添加全文索引
- 使用搜索引擎
- 优化LIKE查询

## ⚠️ 告警建议

| 时间段 | 慢查询数量 | 告警级别 | 处理建议 |
|--------|------------|----------|----------|
| 1小时内 | > 10次 | 🟡 关注 | 检查系统负载 |
| 1小时内 | > 20次 | 🟠 警告 | 需要优化 |
| 1小时内 | > 50次 | 🔴 严重 | 紧急处理 |

## 📈 日常检查

### 每日检查
```bash
# 查看昨天的慢查询数量
grep "$(date -d yesterday +%Y-%m-%d)" log/debug/postdocSlowQuery.log | grep "🐌" | wc -l

# 查看最新的慢查询
tail -5 log/debug/postdocSlowQuery.log
```

### 每周分析
```bash
# 本周慢查询统计
grep -E "$(date -d '7 days ago' +%Y-%m-%d)|$(date -d '6 days ago' +%Y-%m-%d)|$(date -d '5 days ago' +%Y-%m-%d)|$(date -d '4 days ago' +%Y-%m-%d)|$(date -d '3 days ago' +%Y-%m-%d)|$(date -d '2 days ago' +%Y-%m-%d)|$(date -d '1 day ago' +%Y-%m-%d)|$(date +%Y-%m-%d)" log/debug/postdocSlowQuery.log | grep "🐌" | wc -l
```

## 🔧 测试慢查询

```bash
# 触发慢查询测试
php console/scripts/postdoc_comprehensive_test.php announcement 1 3

# 查看生成的慢查询日志
tail -10 log/debug/postdocSlowQuery.log
```

## 📚 相关文档

- 📖 **详细使用说明**: `demand/博士后慢查询监控使用说明.md`
- 📖 **完整监控文档**: `demand/博士后推荐算法性能监控使用文档.md`
- 📖 **快速参考**: `demand/博士后性能监控快速参考.md`

## 💡 优势

### 之前：需要手动筛选
```bash
# 需要从完整日志中筛选慢查询
grep "总执行时间" log/debug/postdocAnnouncementRecommend.log | awk -F'总执行时间: ' '$2 > 2000'
```

### 现在：专用慢查询日志
```bash
# 直接查看，所有记录都是慢查询
tail -f log/debug/postdocSlowQuery.log
```

## 🎉 总结

- **🎯 专注性**: 只关注真正需要优化的慢查询
- **🚀 自动化**: 超过2000ms自动记录，无需手动筛选
- **📊 智能化**: 自动识别主要瓶颈，直接指出优化方向
- **📋 业务化**: 文件名明确标识博士后相关业务

**推荐使用方式**:
```bash
# 开一个终端窗口，专门监控博士后慢查询
tail -f log/debug/postdocSlowQuery.log
```

---
**版本**: v1.0 | **更新**: 2025-08-04 | **业务**: 博士后推荐算法

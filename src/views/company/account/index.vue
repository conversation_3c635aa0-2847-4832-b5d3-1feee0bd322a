<template>
  <div class="main">
    <el-form class="form" label-width="98px">
      <el-row>
        <el-col :span="6">
          <el-form-item label="账号检索">
            <el-input v-model="queryNumberValue" :placeholder="queryNumberPlaceholder" clearable>
              <template #prepend>
                <el-select v-model="queryNumber" style="width: 100px">
                  <el-option
                    v-for="item in queryNumberOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="单位检索">
            <el-input clearable v-model="companyValue" :placeholder="companyPlaceholder">
              <template #prepend>
                <el-select v-model="queryCompany" style="width: 100px">
                  <el-option
                    v-for="item in queryCompanyOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </template>
            </el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="手机号码"
            ><el-input v-model="formData.mobile" placeholder="请填写手机号码"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="邮箱地址">
            <el-input v-model="formData.email" placeholder="请填写邮箱"></el-input
          ></el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="6">
          <el-form-item label="账号类型">
            <el-select class="select" v-model="formData.companyMemberType">
              <el-option
                v-for="item in companyMemberType as any"
                :label="item.v"
                :key="item.k"
                :value="item.k"
              >
                {{ item.v }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="账号权限">
            <el-select class="select" v-model="formData.memberRule">
              <el-option
                v-for="item in memberRule as any"
                :label="item.v"
                :key="item.k"
                :value="item.k"
              >
                {{ item.v }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="账号状态">
            <el-select class="select" v-model="formData.status">
              <el-option
                v-for="item in memberStatus as any"
                :label="item.v"
                :key="item.k"
                :value="item.k"
              >
                {{ item.v }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="注册来源">
            <el-select class="select" v-model="formData.sourceType">
              <el-option
                v-for="item in sourceType as any"
                :label="item.v"
                :key="item.k"
                :value="item.k"
              >
                {{ item.v }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="6">
          <el-form-item label="创建人">
            <el-input v-model="formData.createKeyword" placeholder="请输入创建人账号"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="创建时间">
            <DatePickerRange
              v-model:start="formData.addTimeStart"
              v-model:end="formData.addTimeEnd"
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="最近登录">
            <DatePickerRange
              v-model:start="formData.lastLoginTimeStart"
              v-model:end="formData.lastLoginTimeEnd"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="是否绑定微信" prpp="isWxBind">
            <WxBind placeholder="不限" v-model="formData.isWxBind" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="6">
          <el-form-item label="活跃时间">
            <DatePickerRange
              v-model:start="formData.lastActiveTimeStart"
              v-model:end="formData.lastActiveTimeEnd"
            />
          </el-form-item>
        </el-col>
        <!--        <el-col :span="6">-->
        <!--          <el-form-item label="直聊消息提醒">-->
        <!--            <el-select class="select">-->
        <!--              <el-option-->
        <!--                v-for="item in (isChat as any)"-->
        <!--                :label="item.v"-->
        <!--                :key="item.k"-->
        <!--                :value="item.k"-->
        <!--              >-->
        <!--                {{ item.v }}-->
        <!--              </el-option>-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--        </el-col>-->
        <el-col :span="6">
          <el-form-item label="会员类型" prop="packageType">
            <el-select v-model="formData.packageType" placeholder="不限" filterable clearable>
              <el-option
                v-for="item in unitList.companyPackageList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="单位群组" prop="groupIds">
            <companyGroup v-model="formData.groupIds" :data="companyGroupList" />
          </el-form-item>
        </el-col>

        <el-col :span="5" :offset="1">
          <el-button type="primary" @click="getList">搜索</el-button>
          <el-button @click="reset">重置</el-button>
        </el-col>
      </el-row>
    </el-form>

    <router-link class="add" to="/company/account/add">+ 创建子账号</router-link>

    <el-table class="mt-20 mb-20" border :data="accountList" v-loading="loading">
      <el-table-column prop="memberId" label="账号ID" align="center" />
      <el-table-column prop="username" label="用户名" align="center" />
      <el-table-column prop="contact" label="姓名" align="center" />
      <el-table-column prop="companyName" label="所属单位" align="center">
        <template #default="{ row }">
          <router-link class="td-none color-primary" :to="`/company/details?id=${row.companyId}`">{{
            row.companyName
          }}</router-link>
        </template>
      </el-table-column>
      <el-table-column prop="department" label="所在部门" align="center" />
      <el-table-column prop="groupNames" label="单位群组" align="center" />
      <el-table-column prop="packageType" label="会员类型" align="center" />、
      <el-table-column prop="email" label="绑定邮箱" align="center" />
      <el-table-column prop="companyMemberTypeText" label="账号类型" align="center" />
      <el-table-column prop="memberRuleText" label="账号权限" align="center" width="158px">
        <template #default="{ row }">
          <div class="sort ai-center">
            <div>
              <div>{{ row.memberRuleText }}</div>
              <div v-if="row.companyMemberType === '1'">{{ row.companyVipExpire }}</div>
            </div>
            <img
              v-if="row.memberRule !== '9'"
              class="sort-edit"
              src="/src/assets/icons/edit.svg"
              @click="editAccount(row.id)"
            />
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="addTime" label="创建时间" align="center" />
      <el-table-column prop="lastLoginTime" label="最近登录" align="center" />
      <el-table-column prop="lastLoginArea" label="登录地" align="center" />
      <el-table-column prop="lastActiveTime" label="活跃时间" align="center" />
      <el-table-column prop="statusText" label="账号状态" align="center" />
      <el-table-column prop="sourceTypeText" label="注册来源" align="center" />
      <el-table-column prop="createPersonName" label="创建人" align="center" />
      <el-table-column prop="isBindWechatText" label="是否绑定微信" align="center" />
      <el-table-column label="操作" align="center" width="200px" fixed="right">
        <template #default="{ row }">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-button type="primary" size="small" @click="openDetail(row.id)">查看</el-button>
            </el-col>
            <el-col :span="8">
              <el-button type="success" size="small" @click="handleSetting(row.id)">设置</el-button>
            </el-col>
            <el-col :span="8">
              <el-button
                type="info"
                size="small"
                v-if="row.status === '1'"
                @click="disabledAccount(row)"
                >禁用</el-button
              >
              <el-button type="info" size="small" v-else @click="abledAccount(row)">启用</el-button>
            </el-col>
            <el-col :span="8">
              <el-button type="info" size="small" @click="handleLog(row.memberId)">日志</el-button>
            </el-col>
          </el-row>
        </template>
      </el-table-column>
    </el-table>

    <Paging :total="pages.total" @change="handlePagingChange" />
    <SetAccountDialog ref="setAccount" @update="getList" />
    <AccountDetail ref="accountDetailDialog" />
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, reactive, ref, toRefs, unref, watch } from 'vue'
import { getAccountList, getAccountFilter } from '/@/api/account'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import { ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import SetAccountDialog from './components/setAccountDialog.vue'
import AccountDetail from './components/accountDetail.vue'
import { getUnitList, unitEnable, unitIsAvailable } from '/@/api/unitManage'
import WxBind from '/@/components/base/select/wxBind.vue'
import companyGroup from '/@select/companyGroup.vue'
import { getCompanyGroup } from '/@/api/config'

export default defineComponent({
  name: 'accountList',

  components: { companyGroup, DatePickerRange, Paging, SetAccountDialog, AccountDetail, WxBind },

  setup() {
    const state = reactive({
      accountList: [],
      pages: <any>{},
      formData: {
        groupIds: '',
        packageType: '', // 会员类型
        contact: '',
        companyName: '',
        mobile: '',
        email: '',
        companyMemberType: '',
        memberRule: '',
        status: '',
        sourceType: '',
        createKeyword: '',
        addTimeStart: '',
        addTimeEnd: '',
        lastLoginTimeStart: '',
        lastLoginTimeEnd: '',
        isWxBind: '',
        lastActiveTimeStart: '',
        lastActiveTimeEnd: '',
        page: 1,
        pageSize: 20
      },
      memberRule: [],
      companyMemberType: [],
      sourceType: [],
      memberStatus: [],
      isChat: [],
      loading: false,
      unitList: <any>{},
      companyGroupList: []
    })

    const router = useRouter()
    const route = useRoute()

    const setAccount = ref()

    const accountDetailDialog = ref()

    const findItemData = (target, value) => target.find((item) => item.value === value)

    const queryNumber = ref('1')

    const queryNumberOptions = [
      { label: '姓名', value: '1', key: 'contact', placeholder: '请填写姓名' },
      { label: '账号ID', value: '2', key: 'memberId', placeholder: '请填写账号ID' },
      { label: '用户名', value: '3', key: 'username', placeholder: '请填写用户名' }
    ]

    const queryNumberData = computed(() => findItemData(queryNumberOptions, unref(queryNumber)))

    const queryNumberValue = computed({
      get() {
        return state.formData[unref(queryNumberData).key]
      },

      set(value: string) {
        state.formData[unref(queryNumberData).key] = value
      }
    })

    const queryNumberPlaceholder = computed(() => unref(queryNumberData).placeholder)

    const queryCompany = ref('1')

    const queryCompanyOptions = [
      { label: '单位名称', value: '1', key: 'companyName', placeholder: '请填写单位名称' },
      { label: '单位ID', value: '2', key: 'companyId', placeholder: '请填写单位ID' }
    ]

    const queryCompanyData = computed(() => findItemData(queryCompanyOptions, unref(queryCompany)))

    const companyValue = computed({
      get() {
        return state.formData[unref(queryCompanyData).key]
      },

      set(value: string) {
        state.formData[unref(queryCompanyData).key] = value
      }
    })

    const companyPlaceholder = computed(() => unref(queryCompanyData).placeholder)

    const getQuery = () => {
      const { key: numberKey } = unref(queryNumberData)
      const { key: companyKey } = unref(queryCompanyData)

      const {
        contact,
        groupIds,
        packageType,
        companyName,
        mobile,
        email,
        companyMemberType,
        memberRule,
        status,
        sourceType,
        createKeyword,
        addTimeStart,
        addTimeEnd,
        lastLoginTimeStart,
        lastLoginTimeEnd,
        lastActiveTimeStart,
        lastActiveTimeEnd,
        isWxBind,
        page,
        pageSize
      } = state.formData

      return {
        [numberKey]: state.formData[numberKey],
        [companyKey]: state.formData[companyKey],
        contact,
        groupIds,
        packageType,
        companyName,
        mobile,
        email,
        companyMemberType,
        memberRule,
        status,
        sourceType,
        createKeyword,
        addTimeStart,
        addTimeEnd,
        lastLoginTimeStart,
        lastLoginTimeEnd,
        lastActiveTimeStart,
        lastActiveTimeEnd,
        isWxBind,
        page,
        pageSize
      }
    }

    const getData = async () => {
      state.unitList = await getUnitList()
      state.companyGroupList = await getCompanyGroup()
    }

    const getList = async () => {
      state.loading = true
      const { list, pages } = await getAccountList(getQuery())
      state.accountList = list
      state.pages = pages
      state.loading = false
    }

    const getFilterList = async () => {
      const { memberRule, companyMemberType, sourceType, memberStatus, isChat } =
        await getAccountFilter()
      state.memberRule = memberRule
      state.companyMemberType = companyMemberType
      state.sourceType = sourceType
      state.memberStatus = memberStatus
      state.isChat = isChat
      getList()
    }

    const reset = () => {
      Object.keys(state.formData).forEach((key: string) => {
        state.formData[key] = ''
      })
      getList()
    }

    const editAccount = (id: string) => {
      setAccount.value.open(id)
    }

    const openDetail = (id: string) => {
      accountDetailDialog.value.open(id)
    }

    const disabledAccount = (data: any) => {
      const { memberId, companyName, companyMemberTypeText, companyMemberType } = data
      const companyTypeText =
        companyMemberType === '0'
          ? '该单位下的所有账号均不可登录单位后台'
          : '则无法使用当前账号登录单位后台'
      const tips = `确定要禁用单位${companyName}的${companyMemberTypeText}吗？禁用后${companyTypeText}，请谨慎操作！`

      ElMessageBox.confirm(tips, '确定要禁用该账号吗', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(async () => {
          await unitIsAvailable({ id: memberId })
          getList()
        })
        .catch(() => {})
    }

    const abledAccount = (data: any) => {
      const { memberId, companyName, companyMemberTypeText, companyMemberType } = data
      const companyTypeText =
        companyMemberType === '0'
          ? '该单位下的所有账号均均可登录使用单位后台'
          : '则可使用当前账号登录单位后台'
      const tips = `确定要启用单位${companyName}的${companyMemberTypeText}吗？启用后${companyTypeText}。`
      ElMessageBox.confirm(tips, '确定要启用该账号吗', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(async () => {
          await unitEnable({ id: memberId })
          getList()
        })
        .catch(() => {})
    }

    const handleSetting = (id: string) => {
      router.push(`/company/account/setting/${id}`)
    }

    const handleLog = (id: string) => {
      router.push(`/company/account/log/${id}`)
    }

    watch(
      () => route.query.id,
      (val: string) => {
        if (val) {
          queryCompany.value = '2'
          companyValue.value = val
        }
      },
      { immediate: true }
    )

    const handlePagingChange = (pages: any) => {
      const { page, limit } = pages
      state.formData.page = page
      state.formData.pageSize = limit
      getList()
    }

    getFilterList()
    getData()
    return {
      ...toRefs(state),
      queryNumberValue,
      queryNumberPlaceholder,
      queryNumberOptions,
      queryNumber,
      companyValue,
      queryCompany,
      queryCompanyOptions,
      companyPlaceholder,
      getList,
      reset,
      editAccount,
      setAccount,
      accountDetailDialog,
      openDetail,
      disabledAccount,
      abledAccount,
      handleSetting,
      handleLog,
      handlePagingChange
    }
  }
})
</script>

<style lang="scss" scoped>
.add {
  text-decoration: none;
  background-color: #1c94fa;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
}
.main {
  background-color: white;
  padding: 20px;

  .form {
    margin: 20px 0;

    .select {
      width: 100%;
    }
  }

  .sort {
    display: flex;
    align-items: center;
    justify-content: center;
    .sort-edit {
      width: 20px;
      opacity: 0.6;
      margin-left: 5px;
      cursor: pointer;
    }
  }

  .padding-left-reset {
    :deep(.el-input__wrapper) {
      padding-left: 1px;
    }

    .el-select {
      max-width: 100px;

      :deep(.el-input__wrapper) {
        padding-left: 11px;
      }
    }
  }
}
</style>

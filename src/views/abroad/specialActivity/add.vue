<template>
  <div class="container" v-loading="loading">
    <div class="title mb-20">{{ title }}</div>

    <el-form :model="formData" :rules="rules" label-width="auto" ref="formRef">
      <el-form-item prop="name" label="专场名称">
        <el-input
          @blur="handleNameBlur"
          style="width: 450px"
          v-model="formData.name"
          :maxlength="50"
          placeholder="请输入专场名称，最多50字"
          clearable
        />
      </el-form-item>

      <el-form-item prop="toHoldType" label="举办方式">
        <el-select
          style="width: 450px"
          v-model="formData.toHoldType"
          multiple
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in options.toHoldTypeList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          />
        </el-select>
      </el-form-item>

      <el-form-item prop="type" label="活动类型">
        <el-cascader
          style="width: 450px"
          v-model="formData.type"
          :options="options.typeList"
          :props="typeListProps"
          collapse-tags
          placeholder="请选择"
          clearable
        />
      </el-form-item>

      <el-form-item prop="relationActivityList" label="关联活动">
        <el-input
          v-show="false"
          v-model="formData.relationActivityList"
          placeholder="请添加关联活动"
        />
        <el-button type="primary" @click="openAddActivityDialog">+ 添加活动</el-button>
      </el-form-item>

      <el-form-item label=" ">
        <el-table
          size="small"
          :data="formData.relationActivityList"
          style="width: 100%"
          border
          center
        >
          <el-table-column align="center" header-align="center" prop="name" label="活动名称" />
          <el-table-column
            align="center"
            header-align="center"
            prop="activityDate"
            label="活动日期"
            width="180"
          />
          <el-table-column
            align="center"
            header-align="center"
            prop="activityShort"
            label="活动简称"
          >
            <template #default="{ row }">
              <div class="short-name">
                <div v-if="shortNameEditId === row.activityId" class="input">
                  <el-input
                    v-model="activityShort"
                    :maxlength="30"
                    @keydown.enter="handleEditNameEnter(row)"
                    clearable
                  />
                </div>
                <div v-else class="text" @click="handleEditName(row.activityId, row.activityShort)">
                  {{ row.activityShort }} <i class="el-icon-edit-outline"></i>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" header-align="center" prop="" label="是否推广">
            <template #default="{ row }">
              <el-select v-model="row.isRecommend" placeholder="请选择">
                <el-option label="是" value="1" />
                <el-option label="否" value="2" />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column align="center" header-align="center" prop="sort" label="推广序号">
            <template #default="{ row }">
              <el-input-number
                v-model="row.sort"
                :disabled="row.isRecommend != '1'"
                :value-on-clear="0"
                :controls="false"
                step-strictly
                :min="0"
                clearable
              />
            </template>
          </el-table-column>
          <el-table-column align="center" header-align="center" label="操作">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="removeActivity(row.id)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form-item>

      <el-form-item prop="eventOrganization" label="活动组织">
        <el-input v-model="formData.eventOrganization" :maxlength="100" placeholder=" 请填写" />
      </el-form-item>

      <el-row>
        <el-col :span="8">
          <el-form-item prop="tagIds" label="特色标签">
            <el-select v-model="formData.tagIds" multiple placeholder="请选择" clearable>
              <el-option
                v-for="item in options.activityTagList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="16" class="pl-10">
          <el-form-item prop="customTag" label="">
            <el-input-tag v-model="formData.customTag" placeholder="请录入自定义标签" :max="10" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="5">
          <el-form-item prop="isCustomTime" label="活动时间" required>
            <el-select v-model="formData.isCustomTime" placeholder="请选择" clearable>
              <el-option label="选择时间" value="2" />
              <el-option label="自定义录入" value="1" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="19" class="flex">
          <template v-if="formData.isCustomTime == '1'">
            <el-form-item class="pl-10" prop="customTime">
              <el-input
                style="width: 600px"
                v-model="formData.customTime"
                placeholder="请输入活动时间"
                clearable
              />
            </el-form-item>
          </template>

          <template v-else>
            <el-form-item class="pl-10" prop="startDate">
              <el-date-picker
                v-model="formData.startDate"
                type="date"
                placeholder="开始日期(必填)"
                format="YYYY.MM.DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>

            <el-form-item class="pl-10" prop="startTime">
              <el-time-picker
                v-model="formData.startTime"
                value-format="HHmm"
                placeholder="开始时间(非必填)"
                clearable
              />
            </el-form-item>

            <div style="line-height: 32px; margin: 0 5px">~</div>

            <el-form-item prop="endDate">
              <el-date-picker
                v-model="formData.endDate"
                type="date"
                placeholder="结束日期(必填)"
                format="YYYY.MM.DD"
                value-format="YYYY-MM-DD"
                clearable
              />
            </el-form-item>

            <el-form-item class="pl-10" prop="endTime">
              <el-time-picker
                v-model="formData.endTime"
                value-format="HHmm"
                placeholder="结束时间(非必填)"
                clearable
              />
            </el-form-item>
          </template>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="5">
          <el-form-item prop="isCustomAddress" label="活动地点" required>
            <el-select v-model="formData.isCustomAddress" placeholder="请选择" clearable>
              <el-option label="选择地点" value="2" />
              <el-option label="自定义录入" value="1" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="19" class="flex">
          <template v-if="formData.isCustomAddress == '1'">
            <el-form-item class="pl-10" prop="customAddress">
              <el-input
                style="width: 400px"
                v-model="formData.customAddress"
                placeholder="请输入活动地点"
                clearable
              />
            </el-form-item>
          </template>

          <template v-else>
            <el-form-item class="pl-10" prop="sessionAreaList">
              <el-cascader
                v-model="formData.sessionAreaList"
                :options="options.areaList"
                :props="areaProps"
                collapse-tags
                placeholder="请选择国家城市(必填)"
                clearable
              />
            </el-form-item>
            <el-form-item class="pl-10" prop="detailAddress">
              <el-input
                style="width: 400px"
                v-model="formData.detailAddress"
                placeholder="请输入活动地点"
                clearable
              />
            </el-form-item>
          </template>
        </el-col>
      </el-row>

      <el-form-item prop="activityDetail" label="活动详情">
        <WangEditor
          style="width: 1054px"
          v-model="formData.activityDetail"
          ref="activityDetailRef"
          showCustomUploadFile
        />
      </el-form-item>

      <el-form-item prop="participationMethod" label="参会方式">
        <WangEditor
          style="width: 1054px"
          v-model="formData.participationMethod"
          ref="participationMethodRef"
          showCustomUploadFile
        />
      </el-form-item>

      <el-form-item prop="retrospection" label="往届回顾">
        <WangEditor
          style="width: 1054px"
          v-model="formData.retrospection"
          ref="retrospectionRef"
          showCustomUploadFile
        />
      </el-form-item>

      <el-form-item prop="participationBenefit" label="参会福利">
        <el-input
          style="width: 1054px"
          v-model="formData.participationBenefit"
          :maxlength="100"
          placeholder=" 请输入参会福利概述，最多100字"
        />
      </el-form-item>

      <el-form-item prop="participationBenefitDetail" label="福利详情">
        <WangEditor
          style="width: 1054px"
          v-model="formData.participationBenefitDetail"
          ref="participationBenefitDetailRef"
          showCustomUploadFile
        />
      </el-form-item>

      <el-row>
        <el-form-item class="mr-30" prop="imagePcBannerUrl" label="活动图片" required>
          <div class="upload-img-content">
            <SingleImage
              width="180px"
              uploadText="2560px * 450px<br/>仅支持png、jpg、jpeg格式；"
              v-model="formData.imagePcBannerUrl"
              v-model:id="formData.imagePcBannerId"
            />
            <div class="tips"><span>*</span> PC-banner图</div>
          </div>
        </el-form-item>
        <el-form-item class="mr-30" prop="imageMiniBannerUrl" required>
          <div class="upload-img-content">
            <SingleImage
              width="180px"
              :max-size="5"
              uploadText="750px * 400px<br/>png、jpg、jpeg格式；<br/>限5M以内"
              v-model="formData.imageMiniBannerUrl"
              v-model:id="formData.imageMiniBannerId"
            />
            <div class="tips"><span>*</span> 小程序-banner图</div>
          </div>
        </el-form-item>
        <el-form-item class="mr-30" prop="imageServiceCodeUrl">
          <div class="upload-img-content">
            <SingleImage
              width="180px"
              uploadText="仅支持png、jpg、jpeg格式；"
              v-model="formData.imageServiceCodeUrl"
              v-model:id="formData.imageServiceCodeId"
            />
            <div class="tips">客服二维码</div>
          </div>
        </el-form-item>
      </el-row>

      <el-row>
        <el-col :span="5">
          <el-form-item prop="applyLinkPersonType" label="人才报名链接">
            <el-select
              v-model="formData.applyLinkPersonType"
              @change="personApplyChange"
              placeholder="请选择"
              clearable
            >
              <el-option
                :label="item.v"
                :value="item.k"
                :key="item.k"
                v-for="item in options.applyLinkPersonTypeList"
              />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="19" class="flex">
          <template
            v-if="formData.applyLinkPersonType == '1' || formData.applyLinkPersonType == '3'"
          >
            <el-form-item class="pl-10" prop="applyLinkPersonFormId">
              <el-select
                style="width: 200px"
                v-model="formData.applyLinkPersonFormId"
                placeholder="请选择表单"
                filterable
                remote
                :remote-method="searchFormList"
                @change="handleFormListChange"
                clearable
              >
                <el-option
                  :label="item.v"
                  :value="item.k"
                  :key="item.k"
                  v-for="item in options.applyFormList"
                />
              </el-select>
            </el-form-item>

            <el-form-item
              v-if="formData.applyLinkPersonType == '3'"
              class="pl-10"
              prop="applyLinkPersonFormOptionId"
            >
              <el-select
                style="width: 200px"
                v-model="formData.applyLinkPersonFormOptionId"
                placeholder="请选择选项"
                clearable
              >
                <el-option
                  :label="item.v"
                  :value="item.k"
                  :key="item.k"
                  v-for="item in options.applyFormOptionsList"
                />
              </el-select>
            </el-form-item>
          </template>

          <el-form-item v-else class="pl-10" prop="applyLinkPerson">
            <el-input
              style="width: 300px"
              v-model="formData.applyLinkPerson"
              placeholder="请输入链接"
            />
          </el-form-item>

          <el-form-item class="pl-10" prop="applyPersonTime" label="人才报名截止时间">
            <el-date-picker
              v-model="formData.applyPersonTime"
              type="date"
              placeholder="请选择日期(非必填)"
              format="YYYY.MM.DD"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="8">
          <el-form-item label=" 单位报名链接" prop="applyLinkCompany">
            <el-input
              style="width: 350px"
              v-model="formData.applyLinkCompany"
              placeholder="请录入单位报名链接"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" :offset="1" class="pl-10">
          <el-form-item label="单位报名截止日期" prop="applyCompanyTime">
            <el-date-picker
              v-model="formData.applyCompanyTime"
              type="date"
              placeholder="请选择日期(非必填)"
              format="YYYY.MM.DD"
              value-format="YYYY-MM-DD"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item prop="templateId" label="页面模板">
        <el-select v-model="formData.templateId" placeholder="请选择" clearable>
          <el-option
            v-for="item in options.templateList"
            :key="item.k"
            :label="item.v"
            :value="item.k"
          />
        </el-select>
      </el-form-item>

      <el-form-item prop="specialLink" label="页面链接">
        <el-row align="middle">
          {{ activityDomain }}
          <el-input
            class="pl-10 pr-5"
            style="width: 350px"
            v-model="formData.specialLink"
            placeholder="请输入页面链接,最多50字"
            @input="replaceChinese"
            :maxlength="50"
            clearable
          />
          .html
        </el-row>
      </el-form-item>

      <el-form-item>
        <el-row class="w100" justify="end">
          <el-affix position="bottom" :offset="20">
            <el-button @click="back">取消</el-button>
            <el-button type="primary" @click="save">保存</el-button>
          </el-affix>
        </el-row>
      </el-form-item>
    </el-form>

    <el-dialog
      v-model="addActivityDialogVisible"
      title="添加活动"
      width="500"
      @closed="addActivityClose"
      :close-on-click-modal="false"
    >
      <div>
        <el-select
          v-model="addActivityList"
          value-key="k"
          multiple
          filterable
          remote
          reserve-keyword
          placeholder="请选择活动"
          :remote-method="searchActivity"
        >
          <el-option
            v-for="item in options.activityList"
            :key="item.k"
            :label="item.v"
            :value="item"
          />
        </el-select>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="addActivityDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleActivityAdd"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts"></script>

<script lang="ts" setup>
import { ElMessage } from 'element-plus'
import { onMounted, computed, ref, reactive } from 'vue'
import { useRoute } from 'vue-router'

import WangEditor from '/@/components/wangEditor/index.vue'
import SingleImage from '/@/components/upload/singleImage.vue'

import { getFirstLetter } from '/@/api/config'
import {
  getSpecialActivityDetail,
  getFormList,
  getFormOptionsList,
  getSpecialActivityCanAddActivity,
  updateSpecialActivity
} from '/@/api/abroad'
import router from '/@/router'
defineOptions({ name: 'specialActivityAdd' })
const route = useRoute()

const formRef = ref()
const activityDetailRef = ref()
const participationMethodRef = ref()
const retrospectionRef = ref()
const participationBenefitDetailRef = ref()

const areaProps = { value: 'k', label: 'v', multiple: true, emitPath: false }
const typeListProps = { value: 'k', label: 'v', emitPath: false }

const title = computed(() => {
  const {
    params: { id }
  } = route
  return id ? '编辑专场' : '创建专场'
})

const loading = ref(false)
const shortNameEditId = ref('')
const activityShort = ref('')
const addActivityDialogVisible = ref(false)
const addActivityList = ref([])

const options: any = reactive({
  activityTagList: [],
  applyLinkPersonTypeList: [],
  areaList: [],
  typeList: [],
  templateList: [],
  toHoldTypeList: [],

  activityList: [],

  applyFormList: [],
  applyFormOptionsList: []
})

const hasInput = ref(false)
const activityDomain = ref('')

const formData = reactive({
  id: '',

  name: '',
  toHoldType: '',
  type: '',
  relationActivityList: [],
  eventOrganization: '',
  tagIds: [],
  customTag: [],

  isCustomTime: '2',
  startDate: '',
  startTime: '',
  endDate: '',
  endTime: '',
  customTime: '',

  isCustomAddress: '2',
  customAddress: '',
  sessionAreaList: [],
  detailAddress: '',

  activityDetail: '',
  participationMethod: '',
  retrospection: '',
  participationBenefit: '',
  participationBenefitDetail: '',

  imagePcBannerId: '1',
  imagePcBannerUrl: '',
  imageMiniBannerId: '',
  imageMiniBannerUrl: '',
  imageServiceCodeId: '',
  imageServiceCodeUrl: '',

  applyLinkPersonType: '',
  applyLinkPersonFormId: '',
  applyLinkPersonFormOptionId: '',
  applyLinkPerson: '',
  applyPersonTime: '',

  applyLinkCompany: '',
  applyCompanyTime: '',

  templateId: '2',
  specialLink: ''
})

const rules = ref({
  name: [{ required: true, message: '请输入专场名称', trigger: 'blur' }],
  toHoldType: [{ required: true, message: '请选择举办方式', trigger: 'change' }],

  relationActivityList: [{ required: true, message: '请添加关联活动', trigger: 'blur' }],
  isCustomTime: [{ required: true, message: '请选择', trigger: 'change' }],
  startDate: [
    { required: true, message: '开始日期必填', trigger: 'change' },
    {
      validator: (rule: any, value: string, callback: any) => {
        const { endDate } = formData
        if (!endDate) return callback()
        const isLessThan = new Date(endDate).getTime() < new Date(value).getTime()
        if (isLessThan) return callback(new Error('开始日期不能大于结束日期'))
        return callback()
      },
      trigger: 'change'
    }
  ],
  endDate: [
    { required: true, message: '结束日期必填', trigger: 'change' },
    {
      validator: (rule: any, value: string, callback: any) => {
        const { startDate } = formData
        if (!startDate) return callback()
        const isLessThan = new Date(value).getTime() < new Date(startDate).getTime()
        if (isLessThan) return callback(new Error('结束日期不能小于开始日期'))
        return callback()
      },
      trigger: 'change'
    }
  ],

  isCustomAddress: [{ required: true, message: '请选择', trigger: 'change' }],
  sessionAreaList: [{ required: true, message: '请选择国家城市(必填)', trigger: 'change' }],
  customAddress: [{ required: true, message: '请输入活动地点', trigger: 'blur' }],

  activityDetail: [{ required: true, message: '请输入', trigger: 'blur' }],

  imagePcBannerUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],
  imageMiniBannerUrl: [{ required: true, message: '请上传图片', trigger: 'blur' }],

  applyLinkPersonFormId: [
    {
      required: computed(() => !!formData.applyLinkPersonType),
      message: '请选择',
      trigger: 'change'
    }
  ],
  applyLinkPersonFormOptionId: [
    {
      required: computed(() => !!formData.applyLinkPersonType),
      message: '请选择',
      trigger: 'change'
    }
  ],
  applyLinkPerson: [
    {
      required: computed(() => !!formData.applyLinkPersonType),
      message: '请输入',
      trigger: 'blur'
    }
  ],

  specialLink: [{ required: true, message: '请输入页面链接', trigger: 'blur' }]
})

const handleNameBlur = async () => {
  const { name, id } = formData
  if (name && !hasInput.value && !id) {
    const res = await getFirstLetter(name)
    if (res.text) {
      formData.specialLink = res.text
    }
  }
}

const handleEditName = (id, name) => {
  shortNameEditId.value = id
  activityShort.value = name
}

const handleEditNameEnter = (row) => {
  shortNameEditId.value = ''
  row.activityShort = activityShort.value
}

const removeActivity = (id) => {
  const { relationActivityList } = formData
  const index = relationActivityList.findIndex((item: any) => item.id === id)
  formData.relationActivityList.splice(index, 1)
}

const personApplyChange = () => {
  formData.applyLinkPersonFormId = ''
  formData.applyLinkPersonFormOptionId = ''
  formData.applyLinkPerson = ''
}

const searchFormList = (kw) => {
  getFormOptions({ keyword: kw })
}

const handleFormListChange = (id) => {
  formData.applyLinkPersonFormOptionId = ''

  const { applyLinkPersonType } = formData
  if (applyLinkPersonType !== '3') return
  getFormOptionOptions(id)
}

const getCanAddActivity = (query) => {
  getSpecialActivityCanAddActivity(query).then((resp: any) => {
    options.activityList = resp.list
  })
}

const searchActivity = (kw) => {
  const { relationActivityList } = formData
  const ids = relationActivityList.map((item: any) => item.activityId).join()
  getCanAddActivity({ hasSetActivityId: ids, keyword: kw })
}

const handleActivityAdd = () => {
  const list = addActivityList.value.map((item: any) => {
    const { k, v, activityDate } = item
    return {
      activityId: k,
      name: v,
      activityDate,
      sort: 0,
      isRecommend: '2'
    }
  })
  Array.prototype.push.apply(formData.relationActivityList, list)
  addActivityDialogVisible.value = false
}

const addActivityClose = () => {
  addActivityList.value = []
}

const openAddActivityDialog = () => {
  const { relationActivityList } = formData
  const ids = relationActivityList.map((item: any) => item.activityId).join()
  getCanAddActivity({ hasSetActivityId: ids })
  addActivityDialogVisible.value = true
}

const replaceChinese = (value) => {
  hasInput.value = true
  // 匹配非数字、非字母且非-_/的字符
  const nonAlphanumericRegex = /[^A-Za-z0-9-_\/]/g
  formData.specialLink = value.replace(nonAlphanumericRegex, '')
}

const getDetail = () => {
  const { id } = formData
  loading.value = true
  getSpecialActivityDetail({ id })
    .then((resp: any) => {
      Object.keys(options).forEach((key: string) => {
        options[key] = resp[key] ?? options[key]
      })

      Object.keys(formData).forEach((key: string) => {
        const { info } = resp
        formData[key] = info[key] ? info[key] : formData[key]
      })

      activityDomain.value = resp.activityDomain

      getFormOptions({ formId: formData.applyLinkPersonFormId })
      getFormOptionOptions(formData.applyLinkPersonFormId)
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

const getFormOptions = (query) => {
  getFormList(query).then((resp: any) => {
    options.applyFormList = resp.list
  })
}

const getFormOptionOptions = (id) => {
  getFormOptionsList({ activityFormId: id }).then((resp: any) => {
    options.applyFormOptionsList = resp.list
  })
}

onMounted(() => {
  const {
    params: { id }
  } = route

  formData.id = id ? String(id) : ''

  getDetail()
})

const back = () => {
  router.back()
}

const save = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      loading.value = true
      const {
        tagIds = [],
        customTag = [],
        isCustomTime,
        startDate,
        startTime,
        endDate,
        endTime,
        customTime,
        isCustomAddress,
        sessionAreaList,
        detailAddress,
        customAddress,
        ...postData
      } = formData
      updateSpecialActivity({
        ...postData,
        startDate: isCustomTime === '1' ? '' : startDate,
        startTime: isCustomTime === '1' ? '' : startTime,
        endDate: isCustomTime === '1' ? '' : endDate,
        endTime: isCustomTime === '1' ? '' : endTime,
        customTime: isCustomTime === '1' ? customTime : '',
        sessionAreaList: isCustomAddress === '1' ? '' : sessionAreaList,
        detailAddress: isCustomAddress === '1' ? '' : detailAddress,
        customAddress: isCustomAddress === '1' ? customAddress : '',
        tagIds: tagIds.join(),
        customTag: customTag.join()
      })
        .then((r) => {
          loading.value = false
          if (!formData.id) {
            formRef.value.resetFields()

            activityDetailRef.value.clearEditor()
            participationMethodRef.value.clearEditor()
            retrospectionRef.value.clearEditor()
            participationBenefitDetailRef.value.clearEditor()
          }

          if (!formData.id) {
            // 替换当前标签页到编辑页面
            router.back()
          }

          // 初始化数据
          // getDetail()
        })
        .catch(() => {
          loading.value = false
        })
    }
  })
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
  background-color: var(--color-whites);

  .title {
    font-size: 15px;
    font-weight: bold;
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 10px;
  }

  .upload-img-content {
    text-align: center;

    .tips {
      span {
        color: var(--el-color-danger);
      }
    }
  }

  .short-name {
    .el-icon-edit-outline {
      color: var(--color-primary);
    }
  }
}
</style>

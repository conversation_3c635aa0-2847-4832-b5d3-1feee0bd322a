import { RouteRecordRaw } from 'vue-router'
import { cmsRoutes } from './module/cms'
import { companyRoutes } from './module/company'
import { permissionsRoutes } from './module/permissions'
import { personRoutes } from './module/person'
import { systemRoutes } from './module/system'
import { testRoutes } from './module/test'
import { jobRoutes } from './module/job'
// import { memberRoutes } from './module/member'
import { configurationRoutes } from './module/configuration'
import { announcementRoutes } from './module/announcement'
import { seoRoutes } from './module/seo'
import { statementRoutes } from './module/statement'
import { abroadRoutes } from './module/abroad'
import { managementRoutes } from './module/management'
import { publishRoutes } from './module/publish'
import { advertisingRoutes } from './module/advertising'
import { informationRoutes } from './module/information'
import { specialNeedRoutes } from './module/specialNeed'
/**
 * 路由meta对象参数说明
 * meta: {
 *      title:          菜单栏及 tagsView 栏、菜单搜索名称（国际化）
 *      isLink：        是否超链接菜单，开启外链条件，`1、isLink:true 2、链接地址不为空`
 *      isHide：        是否隐藏此路由
 *      isKeepAlive：   是否缓存组件状态
 *      isAffix：       是否固定在 tagsView 栏上
 *      isIframe：      是否内嵌窗口，，开启条件，`1、isIframe:true 2、链接地址不为空`
 *      icon：          菜单、tagsView 图标，阿里：加 `iconfont xxx`，fontawesome：加 `fa xxx`
 * }
 */

/**
 * 定义动态路由
 * @description 未开启 isRequestRoutes 为 true 时使用（前端控制路由），开启时第一个顶级 children 的路由将被替换成接口请求回来的路由数据
 * @description 各字段请查看 `/@/views/system/menu/component/addMenu.vue 下的 ruleForm`
 * @returns 返回路由菜单数据
 */
export const dynamicRoutes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: '/',
    component: () => import('/@/layout/index.vue'),
    redirect: '/home',
    meta: {
      isKeepAlive: true
    },
    children: [
      {
        path: '/home',
        name: 'home',
        component: () => import('/@/views/home/<USER>'),
        meta: {
          title: '首页',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: true,
          isIframe: false,
          icon: '/src/assets/icons/home.svg'
        }
      }
    ]
  },
  ...testRoutes,
  ...systemRoutes,
  ...personRoutes,
  ...publishRoutes,
  ...cmsRoutes,
  ...companyRoutes,
  // ...memberRoutes,
  ...configurationRoutes,
  ...announcementRoutes,
  ...jobRoutes,
  ...advertisingRoutes,
  ...informationRoutes,
  // ...memberRoutes,
  ...seoRoutes,
  ...statementRoutes,
  ...managementRoutes,
  ...permissionsRoutes,
  ...abroadRoutes,
  ...specialNeedRoutes
]

/**
 * 定义静态路由
 * @description 前端控制直接改 dynamicRoutes 中的路由，后端控制不需要修改，请求接口路由数据时，会覆盖 dynamicRoutes 第一个顶级 children 的内容（全屏，不包含 layout 中的路由出口）
 * @returns 返回路由菜单数据
 */
export const staticRoutes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'login',
    component: () => import('/@/views/login/index.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/404',
    name: 'notFound',
    component: () => import('/@/views/error/404.vue'),
    meta: {
      title: '页面不存在'
    }
  },
  {
    path: '/401',
    name: 'noPower',
    component: () => import('/@/views/error/401.vue'),
    meta: {
      title: 'message.staticRoutes.noPower'
    }
  },
  {
    path: '/test',
    name: 'test',
    component: () => import('/@/views/test/index.vue'),
    meta: {
      title: '测试'
    }
  },
  {
    path: '/person/detail/:id',
    name: 'personDetail',
    component: () => import('/@/views/person/detail/index.vue'),
    meta: {
      title: '人才详情',
      isLink: '',
      isHide: true,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: 'iconfont icon-caidan'
    }
  }
]
